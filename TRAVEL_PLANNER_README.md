# AutoPilot AI - 智能旅行规划Agent

基于AI的智能旅行规划系统，提供个性化、实时的旅行行程规划服务。

## 🌟 主要特性

### 核心功能
- **智能意图理解**: 自然语言理解用户旅行需求
- **个性化推荐**: 基于用户偏好和历史记录的智能推荐
- **实时规划进度**: Server-Sent Events流式输出规划过程
- **高德地图集成**: 地理定位、路线规划、POI搜索
- **记忆学习系统**: 从用户交互中学习，持续优化推荐

### 技术特色
- **原子化能力**: 模块化的工具和服务设计
- **流式输出**: 实时展示AI思考过程
- **多数据库支持**: MongoDB + MySQL + Redis
- **现代化前端**: 响应式Web界面
- **RESTful API**: 完整的API接口

## 🏗️ 系统架构

```
autopilotai/
├── src/                          # 核心源码
│   ├── agents/                   # Agent实现
│   │   └── travel_planner_agent.py
│   ├── api/                      # API接口
│   │   └── travel_planner.py
│   ├── core/                     # 核心模块
│   │   ├── config.py            # 配置管理
│   │   ├── logger.py            # 日志系统
│   │   └── llm_manager.py       # LLM管理
│   ├── database/                 # 数据库操作
│   │   └── mongodb_client.py
│   ├── memory/                   # 记忆系统
│   │   └── memory_manager.py
│   ├── models/                   # 数据模型
│   │   ├── travel_planner.py
│   │   └── database.py
│   ├── tools/                    # 工具集成
│   │   └── amap_mcp_client.py
│   └── main.py                   # 应用入口
├── static/                       # 前端资源
│   ├── index.html               # 主页面
│   ├── css/style.css            # 样式文件
│   └── js/app.js                # 前端逻辑
├── config/                       # 配置文件
│   └── default.yaml
├── start_server.py              # 启动脚本
└── test_travel_planner.py       # 测试脚本
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保Python 3.9+
python --version

# 激活虚拟环境
.\venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -e .
```

### 2. 配置检查

系统已预配置数据库连接：
- **MongoDB**: ***********:27017 (admin/m25uids*g)
- **MySQL**: ***********:19090 (root/Fsit#2024)
- **Redis**: ***********:5182 (kLKe3NFM4RZMgXhA)
- **高德地图**: 已配置API密钥

### 3. 启动服务

```bash
# 检查环境
python start_server.py --check-only

# 运行测试
python start_server.py --test

# 启动开发服务器
python start_server.py --reload

# 启动生产服务器
python start_server.py --host 0.0.0.0 --port 8000 --workers 4
```

### 4. 访问服务

- **前端界面**: http://localhost:8000/static/index.html
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## 📱 使用指南

### Web界面使用

1. **打开前端页面**: 访问 http://localhost:8000/static/index.html
2. **输入旅行需求**: 在左侧面板描述你的旅行想法
3. **开始规划**: 点击"开始规划"按钮
4. **查看进度**: 实时观看AI思考过程和规划进度
5. **查看结果**: 在右侧查看生成的详细行程

### API接口使用

#### 创建规划任务
```bash
curl -X POST "http://localhost:8000/api/travel/plan" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "我想去上海玩3天，喜欢文化景点和美食",
    "user_id": "user_001"
  }'
```

#### 流式获取规划进度
```bash
curl "http://localhost:8000/api/travel/plan/{trace_id}/stream?user_id=user_001&query=..."
```

#### 获取规划结果
```bash
curl "http://localhost:8000/api/travel/plan/{trace_id}"
```

## 🔧 核心组件

### 1. TravelPlannerAgent
智能旅行规划Agent，负责：
- 意图理解和实体提取
- 工具规划和并行执行
- 流式输出和进度反馈

### 2. 高德MCP工具
集成高德地图服务：
- 地理编码和逆地理编码
- POI搜索和详情查询
- 路线规划和导航
- 天气查询

### 3. 记忆系统
用户记忆管理：
- 偏好学习和存储
- 相关记忆检索
- 个性化推荐

### 4. 数据库设计
- **MongoDB**: 存储行程、用户、记忆数据
- **MySQL**: 结构化数据存储
- **Redis**: 缓存和会话管理

## 🎯 功能演示

### 示例查询
```
"我想去上海玩3天，喜欢文化景点和美食，预算3000元左右，希望住在市中心"
```

### 规划过程
1. **意图理解**: 提取目的地、天数、偏好、预算等信息
2. **地理定位**: 获取上海的地理坐标
3. **天气查询**: 获取旅行期间的天气预报
4. **POI搜索**: 搜索文化景点、美食、酒店
5. **行程规划**: 生成每日详细行程
6. **路线规划**: 规划景点间的最优路线
7. **预算估算**: 计算各项费用预算

### 输出结果
- 完整的多日行程安排
- 每日POI推荐和路线
- 预算明细和建议
- 天气信息和出行提醒
- 个人地图和导航链接

## 🧪 测试

### 运行完整测试
```bash
python test_travel_planner.py
```

### 测试项目
- ✅ 配置加载
- ✅ 数据库连接
- ✅ 高德地图工具
- ✅ 记忆系统
- ✅ 旅行规划Agent
- ✅ API端点

## 📊 监控和日志

### 健康检查
```bash
curl http://localhost:8000/health
```

### 日志查看
日志文件位于 `logs/` 目录，使用结构化JSON格式。

### 性能监控
- 请求响应时间
- 数据库连接状态
- 内存使用情况
- API调用统计

## 🔒 安全考虑

- API密钥安全存储
- 用户数据隐私保护
- 输入验证和清理
- 错误信息脱敏

## 🚧 开发计划

### 近期计划
- [ ] 地图可视化功能
- [ ] 行程编辑功能
- [ ] 用户认证系统
- [ ] 移动端适配

### 长期计划
- [ ] 多语言支持
- [ ] 语音交互
- [ ] 社交分享功能
- [ ] 智能客服集成

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 支持

如有问题或建议，请联系开发团队。

---

**AutoPilot AI Team** - 让AI为你的旅行导航 ✈️
