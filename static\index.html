<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoPilot AI - 智能旅行规划</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-airplane"></i>
                AutoPilot AI
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#home">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#planner">智能规划</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#history">历史行程</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧规划面板 -->
            <div class="col-lg-4 col-md-5 planning-panel">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-magic"></i>
                            智能旅行规划
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 规划表单 -->
                        <form id="planningForm">
                            <div class="mb-3">
                                <label for="userQuery" class="form-label">告诉我你的旅行想法</label>
                                <textarea 
                                    class="form-control" 
                                    id="userQuery" 
                                    rows="4" 
                                    placeholder="例如：我想去上海玩3天，喜欢文化景点和美食，预算3000元左右..."
                                    required
                                ></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="userId" class="form-label">用户ID</label>
                                <input 
                                    type="text" 
                                    class="form-control" 
                                    id="userId" 
                                    value="user_001" 
                                    required
                                >
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100" id="planButton">
                                <i class="bi bi-play-circle"></i>
                                开始规划
                            </button>
                        </form>

                        <!-- 规划进度 -->
                        <div id="planningProgress" class="mt-4" style="display: none;">
                            <h6>规划进度</h6>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" 
                                     style="width: 0%"
                                     id="progressBar">
                                </div>
                            </div>
                            <div id="progressText" class="text-muted small">
                                准备开始规划...
                            </div>
                        </div>

                        <!-- 思考过程 -->
                        <div id="thinkingProcess" class="mt-4" style="display: none;">
                            <h6>AI思考过程</h6>
                            <div id="thinkingSteps" class="thinking-steps">
                                <!-- 思考步骤将在这里动态添加 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧结果展示区域 -->
            <div class="col-lg-8 col-md-7 result-panel">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-map"></i>
                            旅行规划结果
                        </h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="viewModeList">
                                <i class="bi bi-list-ul"></i>
                                列表视图
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="viewModeMap">
                                <i class="bi bi-geo-alt"></i>
                                地图视图
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 欢迎界面 -->
                        <div id="welcomeView" class="text-center py-5">
                            <i class="bi bi-compass display-1 text-muted"></i>
                            <h3 class="mt-3 text-muted">开始你的智能旅行规划</h3>
                            <p class="text-muted">在左侧输入你的旅行想法，AI将为你生成个性化的旅行行程</p>
                        </div>

                        <!-- 行程概览 -->
                        <div id="itinerarySummary" style="display: none;">
                            <div class="row mb-4">
                                <div class="col-md-8">
                                    <h4 id="itineraryTitle">行程标题</h4>
                                    <p class="text-muted" id="itineraryDescription">行程描述</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="btn-group">
                                        <button class="btn btn-outline-success btn-sm" id="saveItinerary">
                                            <i class="bi bi-save"></i>
                                            保存行程
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" id="editItinerary">
                                            <i class="bi bi-pencil"></i>
                                            编辑
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" id="shareItinerary">
                                            <i class="bi bi-share"></i>
                                            分享
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 行程统计 -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-calendar-event"></i>
                                        </div>
                                        <div class="stat-info">
                                            <div class="stat-value" id="totalDays">0</div>
                                            <div class="stat-label">天数</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-geo-alt"></i>
                                        </div>
                                        <div class="stat-info">
                                            <div class="stat-value" id="totalPOIs">0</div>
                                            <div class="stat-label">景点</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-currency-dollar"></i>
                                        </div>
                                        <div class="stat-info">
                                            <div class="stat-value" id="estimatedBudget">¥0</div>
                                            <div class="stat-label">预算</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-cloud-sun"></i>
                                        </div>
                                        <div class="stat-info">
                                            <div class="stat-value" id="weatherInfo">晴</div>
                                            <div class="stat-label">天气</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 每日行程 -->
                        <div id="dailyItinerary" style="display: none;">
                            <div id="dailyPlans">
                                <!-- 每日计划将在这里动态生成 -->
                            </div>
                        </div>

                        <!-- 地图视图 -->
                        <div id="mapView" style="display: none;">
                            <div id="mapContainer" style="height: 500px; background: #f8f9fa; border-radius: 8px;">
                                <div class="d-flex align-items-center justify-content-center h-100">
                                    <div class="text-center">
                                        <i class="bi bi-map display-4 text-muted"></i>
                                        <p class="text-muted mt-2">地图功能开发中...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史行程模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">历史行程</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="historyList">
                        <!-- 历史行程列表 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中提示 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-3">AI正在为您规划行程...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>
